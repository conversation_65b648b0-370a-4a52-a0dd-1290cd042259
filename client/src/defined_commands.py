import base64
import functools
import logging
from io import By<PERSON><PERSON>
from typing import Any

from telegram import Update, ForceReply
from telegram._utils.types import JSONDict
from telegram.ext import ContextTypes, CommandHandler, Application

import bag_service
import crypto_data_client
import user_service

users = user_service.UserService()
crypto_data = crypto_data_client.CryptoDataClient()
bags = bag_service.BagService(users, crypto_data)

logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)


async def send_reply(update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, **kwargs) -> None:
    """
    Send a reply that works for both regular messages and channel posts.

    For regular messages (private chats, groups): uses reply_text()
    For channel posts: uses send_message() to the channel
    """
    if update.message:
        # Regular message - can use reply_text
        await update.message.reply_text(text, **kwargs)
    elif update.channel_post:
        # Channel post - need to send a new message to the channel
        await context.bot.send_message(chat_id=update.effective_chat.id, text=text, **kwargs)
    elif update.effective_chat and update.effective_message:
        # Fallback using effective_chat and effective_message
        await context.bot.send_message(chat_id=update.effective_chat.id, text=text, **kwargs)
    else:
        # Last resort - log the issue
        logger.error(f"Could not send reply - no valid message or chat found in update: {update}")
        raise ValueError("No valid message or chat found in update")


async def send_reply_markdown_v2(update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, **kwargs) -> None:
    """
    Send a markdown v2 reply that works for both regular messages and channel posts.
    """
    if update.message:
        # Regular message - can use reply_markdown_v2
        await update.message.reply_markdown_v2(text, **kwargs)
    elif update.channel_post:
        # Channel post - need to send a new message to the channel
        await context.bot.send_message(chat_id=update.effective_chat.id, text=text, parse_mode='MarkdownV2', **kwargs)
    elif update.effective_chat and update.effective_message:
        # Fallback using effective_chat and effective_message
        await context.bot.send_message(chat_id=update.effective_chat.id, text=text, parse_mode='MarkdownV2', **kwargs)
    else:
        # Last resort - log the issue
        logger.error(f"Could not send reply - no valid message or chat found in update: {update}")
        raise ValueError("No valid message or chat found in update")


async def send_photo(update: Update, context: ContextTypes.DEFAULT_TYPE, photo, **kwargs) -> None:
    """
    Send a photo that works for both regular messages and channel posts.
    """
    chat_id = update.effective_chat.id if update.effective_chat else None
    if not chat_id:
        logger.error(f"Could not send photo - no valid chat found in update: {update}")
        raise ValueError("No valid chat found in update")

    await context.bot.send_photo(chat_id=chat_id, photo=photo, **kwargs)


def add(application: Application) -> None:
    """
    Add all the defined commands to the dispatcher
    :param dispatcher:
    """
    for command in commands:
        application.add_handler(CommandHandler(command[0], command[1]))


def _add_user(func) -> Any:
    async def wrapper(*args, **kwargs):
        logger.info('Adding user')
        update = args[0]
        # Handle both regular messages and channel posts
        user_data = None
        if update.message and update.message.from_user:
            user_data = update.message.from_user.to_dict()
        elif update.channel_post and update.channel_post.from_user:
            user_data = update.channel_post.from_user.to_dict()
        elif update.effective_user:
            user_data = update.effective_user.to_dict()

        if user_data:
            users.add_new_user(user_data)
        else:
            logger.warning("Could not extract user data from update")

        return await func(*args, **kwargs)

    return wrapper


def _update_command_calls(func) -> Any:
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        callback = args[1]
        logger.info(f'arguments-{callback.args}')
        await func(*args, **kwargs)
        update = args[0]

        # Handle both regular messages and channel posts
        message_text = None
        if update.message and update.message.text:
            message_text = update.message.text
        elif update.channel_post and update.channel_post.text:
            message_text = update.channel_post.text
        elif update.effective_message and update.effective_message.text:
            message_text = update.effective_message.text

        if message_text:
            users.update_command_calls(extract_current_request_data_from_update(update), message_text)
        else:
            logger.warning("Could not extract message text from update")

        return None

    return wrapper


@_add_user
@_update_command_calls
async def _start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    user = update.effective_user
    await send_reply_markdown_v2(
        update, context,
        fr'Hi, {user.mention_markdown_v2()}\! I am here to help you with your crypto needs\. Type /help to see what I can do\.',
        reply_markup=ForceReply(selective=True),
    )


@_add_user
@_update_command_calls
async def _send_market_cap(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await send_reply(update, context, await crypto_data.get_mcap_summary())


@_add_user
@_update_command_calls
async def _send_cummies(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await send_reply(update, context, await crypto_data.get_coin_summary('cumrocket'))


@_add_user
@_update_command_calls
async def coin_price(update: Update, cb: ContextTypes.DEFAULT_TYPE) -> None:
    words_in_text = cb.args
    if len(words_in_text) != 1:
        logger.info("Too many args")
        return

    await send_reply(update, cb, await crypto_data.get_coin_summary(words_in_text.pop()))


@_add_user
@_update_command_calls
async def _send_help(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await send_reply(update, context,
        '\n'.join(list(map(lambda command: '/' + command[0] + ' : ' + command[2], commands))))


@_add_user
@_update_command_calls
async def _send_two_year_chart(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await send_photo(update, context, photo=BytesIO(base64.b64decode(
        await crypto_data.get_2_year_avg_chart())))


@_add_user
@_update_command_calls
async def _send_rainbow_chart(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await send_photo(update, context, photo=BytesIO(base64.b64decode(
        await crypto_data.get_rainbow_chart())))


@_add_user
@_update_command_calls
async def _send_trending(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await send_reply(update, context, await crypto_data.get_trending_coins())


@_add_user
@_update_command_calls
async def _add_coin_to_bag(update: Update, cb: ContextTypes.DEFAULT_TYPE) -> None:
    await send_reply(update, cb, await bags.add_coin(extract_current_request_data_from_update(update), cb))


@_add_user
@_update_command_calls
async def _remove_coin_from_bag(update: Update, cb: ContextTypes.DEFAULT_TYPE) -> None:
    await send_reply(update, cb, await bags.remove_from_bag(extract_current_request_data_from_update(update), cb))


@_add_user
@_update_command_calls
async def _send_bag_data(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await send_reply(update, context, await bags.get_bag_data(extract_current_request_data_from_update(update)))


@_add_user
async def _send_last_ten_commands(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    last_commands = users.get_last_ten_commands(extract_current_request_data_from_update(update))
    if len(last_commands) < 1:
        await send_reply(update, context, "Sorry, did not find your command history!\nTry using /help")
    else:
        last_commands.insert(0, 'Your last 10 commands🧐')
        await send_reply(update, context, "\n".join(last_commands))


def extract_current_request_data_from_update(update: Update) -> JSONDict:
    """Extract user data from update, handling both regular messages and channel posts."""
    user_data = {}

    if update.message and update.message.from_user:
        user_data = update.message.from_user.to_dict()
    elif update.channel_post and update.channel_post.from_user:
        user_data = update.channel_post.from_user.to_dict()
    elif update.effective_user:
        user_data = update.effective_user.to_dict()
    else:
        logger.error(f"Could not extract user data from update: {update}")
        return {}

    # Ensure the 'id' key exists - sometimes it might be missing or have a different name
    if 'id' not in user_data:
        # Try common alternative key names
        if 'user_id' in user_data:
            user_data['id'] = user_data['user_id']
        elif 'telegram_id' in user_data:
            user_data['id'] = user_data['telegram_id']
        else:
            logger.error(f"No user ID found in user data: {user_data}")
            # For channel posts, we might not have a user ID, so create a fallback
            user_data['id'] = 0  # Use 0 as fallback for anonymous channel posts

    return user_data


commands = [
    ("start", _start, "Start conversation"),
    ("help", _send_help, "List commands"),
    ("cummies", _send_cummies, "Show summary for $CUMMIES"),
    ("price", coin_price, "Show summary for a coin, e.g. ´/price@bitcoin´"),
    ("mcap", _send_market_cap, "Show CMC summary"),
    ("last10", _send_last_ten_commands, "Show last ten used commands"),
    ("2year", _send_two_year_chart, "Show btc 2-Year MA Multiplier"),
    ("rainbow", _send_rainbow_chart, "Show btc rainbow graph"),
    ("trending", _send_trending, "Show trending coins"),
    ("bag_add", _add_coin_to_bag, "Add a coin to the bag, e.g. ´/bag_add bitcoin 0.001´"),
    ("bag_remove", _remove_coin_from_bag, "Remove a coin from the bag, e.g. ´/bag_remove bitcoin`"),
    ("bag_show", _send_bag_data, "Show off your bag")
]
